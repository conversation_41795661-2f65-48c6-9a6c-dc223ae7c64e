{"timestamp": "2025-07-26T18:31:40.167Z", "status": {"homePage": {"url": "https://adawat.org", "statusCode": 200, "loadTime": 588, "accessible": true, "size": 282983, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:31:40.760Z"}, "sitemap": {"accessible": true, "urlCount": 322, "size": 52199, "lastModified": "<PERSON>ي<PERSON> محدد"}, "robots": {"accessible": true, "hasSitemap": true, "allowsAll": true, "size": 1961}, "importantPages": {"/tools/zakat-calculator": {"url": "https://adawat.org/tools/zakat-calculator", "statusCode": 200, "loadTime": 77, "accessible": true, "size": 20445, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:31:40.990Z"}, "/tools/date-converter": {"url": "https://adawat.org/tools/date-converter", "statusCode": 200, "loadTime": 77, "accessible": true, "size": 20412, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:31:41.067Z"}, "/tools/age-calculator": {"url": "https://adawat.org/tools/age-calculator", "statusCode": 200, "loadTime": 76, "accessible": true, "size": 20406, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:31:41.143Z"}, "/tools/currency-converter": {"url": "https://adawat.org/tools/currency-converter", "statusCode": 200, "loadTime": 78, "accessible": true, "size": 20449, "hasTitle": false, "hasDescription": false, "hasH1": false, "hasStructuredData": true, "timestamp": "2025-07-26T18:31:41.221Z"}}}, "performance": {}, "issues": ["الصفحة الرئيسية لا تحتوي على title", "الصفحة الرئيسية لا تحتوي على meta description"], "recommendations": ["جميع الصفحات المهمة متاحة ✅", "ركز على حل المشاكل التقنية أولاً"]}